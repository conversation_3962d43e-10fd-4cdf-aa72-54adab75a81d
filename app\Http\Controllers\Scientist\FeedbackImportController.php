<?php

namespace App\Http\Controllers\Scientist;

use App\Http\Controllers\Controller;
use App\Models\ActionPlan;
use App\Models\UserFeedback;
use App\Exports\UserFeedbackTemplateExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class FeedbackImportController extends Controller
{
    /**
     * Display the feedback import page.
     */
    public function index()
    {
        return view('scientist.feedback.index');
    }

    /**
     * Get all action plans for the current scientist.
     */
    public function getEvents()
    {
        try {
            Log::info('Getting action plans for scientist ID: ' . Auth::id());

            // Get action plans for the current scientist
            $actionPlans = ActionPlan::where('scientist_id', Auth::id())
                ->with(['scientist:id,name,email', 'district:id,state,district'])
                ->orderBy('planned_date', 'desc')
                ->get();

            Log::info('Found action plans for scientist', [
                'scientist_id' => Auth::id(),
                'count' => $actionPlans->count()
            ]);

            $actionPlansWithFeedback = $actionPlans->map(function ($plan) {
                // Format the planned date
                $formattedDate = null;
                if ($plan->planned_date) {
                    try {
                        $formattedDate = \Carbon\Carbon::parse($plan->planned_date)->format('Y-m-d');
                    } catch (\Exception $e) {
                        Log::warning('Invalid date format for action plan ' . $plan->id . ': ' . $plan->planned_date);
                        $formattedDate = $plan->planned_date;
                    }
                }

                // Count existing feedback from user_feedback table
                $feedbackCount = UserFeedback::where('event_id', $plan->id)->count();

                return [
                    'id' => $plan->id,
                    'title' => $plan->title ?? 'N/A',
                    'planned_date' => $formattedDate ?? 'N/A',
                    'location' => $plan->location ?? 'N/A',
                    'status' => $plan->status ?? 'N/A',
                    'type' => $plan->type ?? 'N/A',
                    'scientist' => $plan->scientist ? $plan->scientist->name : 'N/A',
                    'district' => $plan->district ? $plan->district->district : 'N/A',
                    'feedback_count' => $feedbackCount,
                    'can_add_review' => $this->canAddReview($plan)
                ];
            });

            return response()->json($actionPlansWithFeedback);

        } catch (\Exception $e) {
            Log::error('Error getting action plans for scientist: ' . $e->getMessage(), [
                'scientist_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['error' => 'Failed to load action plans. Please try again later.'], 500);
        }
    }

    /**
     * Check if an action plan is within the 2-day window for adding reviews
     */
    private function canAddReview($actionPlan)
    {
        try {
            if (!$actionPlan->planned_date) {
                return false;
            }

            // Ensure we have a Carbon instance
            $plannedDate = $actionPlan->planned_date instanceof \Carbon\Carbon
                ? $actionPlan->planned_date
                : \Carbon\Carbon::parse($actionPlan->planned_date);

            // For testing purposes, allow reviews for action plans within 30 days
            // TODO: Change back to 2 days in production
            $thirtyDaysAgo = now()->subDays(2);
            return $plannedDate->greaterThanOrEqualTo($thirtyDaysAgo);
        } catch (\Exception $e) {
            Log::error('Error in canAddReview for plan ' . $actionPlan->id . ': ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Get feedback for a specific event.
     */
    public function getEventFeedback($eventId)
    {
        try {
            // Verify the action plan belongs to the current scientist
            $actionPlan = ActionPlan::where('id', $eventId)
                ->where('scientist_id', Auth::id())
                ->with(['scientist:id,name,email', 'district:id,state,district'])
                ->first();

            if (!$actionPlan) {
                return view('scientist.feedback.error', [
                    'message' => 'Action plan not found or you do not have permission to view it.'
                ]);
            }

            // Get feedback from user_feedback table (our new simplified feedback)
            $feedback = UserFeedback::where('event_id', $eventId)
                ->orderBy('created_at', 'desc')
                ->get();

            // Calculate statistics
            $stats = [
                'total_feedback' => $feedback->count(),
                'avg_benefit_rating' => round($feedback->avg('benefit_rating') ?? 0, 1),
                'avg_speaker_rating' => round($feedback->avg('speaker_rating') ?? 0, 1),
                'recommend_percentage' => $feedback->count() > 0 ?
                    round(($feedback->where('would_recommend', 1)->count() / $feedback->count()) * 100, 1) : 0,
                'total_responses' => $feedback->count(),
            ];

            Log::info('Retrieved feedback for action plan', [
                'action_plan_id' => $eventId,
                'feedback_count' => $feedback->count()
            ]);

            // Return a proper view instead of JSON
            return view('scientist.feedback.feedback-details', [
                'actionPlan' => $actionPlan,
                'feedback' => $feedback,
                'stats' => $stats,
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting action plan feedback: ' . $e->getMessage());
            return view('scientist.feedback.error', [
                'message' => 'An error occurred while loading feedback. Please try again.'
            ]);
        }
    }

    /**
     * Download template for feedback import.
     */
    public function downloadTemplate(Request $request)
    {
        try {
            $eventId = $request->get('event_id');
            
            if ($eventId) {
                // Verify the action plan belongs to the current scientist
                $actionPlan = ActionPlan::where('id', $eventId)
                    ->where('scientist_id', Auth::id())
                    ->first();

                if (!$actionPlan) {
                    return response()->json(['error' => 'Action plan not found or access denied'], 404);
                }
            }

            return Excel::download(new UserFeedbackTemplateExport, 'feedback_template.xlsx');

        } catch (\Exception $e) {
            Log::error('Error downloading template: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to download template'], 500);
        }
    }

    /**
     * Import feedback from Excel file.
     */
    public function importFeedback(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|mimes:xlsx,xls',
                'event_id' => 'required|exists:action_plans,id'
            ]);

            // Verify the action plan belongs to the current scientist
            $actionPlan = ActionPlan::where('id', $request->event_id)
                ->where('scientist_id', Auth::id())
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found or access denied'], 404);
            }

            $file = $request->file('file');
            $data = Excel::toArray([], $file);

            if (empty($data) || empty($data[0])) {
                return response()->json(['error' => 'The uploaded file is empty or invalid'], 400);
            }

            $rows = $data[0];
            array_shift($rows); // Remove header row

            $importedCount = 0;
            $errors = [];

            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2; // +2 because we removed header and arrays are 0-indexed

                try {
                    // Skip empty rows
                    if (empty(array_filter($row))) {
                        continue;
                    }

                    // Map columns (adjust indices based on your template)
                    $name = trim($row[0] ?? '');
                    $phoneNumber = trim($row[1] ?? '');
                    $benefitRating = trim($row[2] ?? '');
                    $wouldRecommend = trim($row[3] ?? '');
                    $mostHelpfulTopic = trim($row[4] ?? '');
                    $speakerRating = trim($row[5] ?? '');
                    $suggestions = trim($row[6] ?? '');

                    // Validate required fields
                    if (empty($name)) {
                        $errors[] = "Row {$rowNumber}: Name is required";
                        continue;
                    }

                    if (empty($phoneNumber)) {
                        $errors[] = "Row {$rowNumber}: Phone number is required";
                        continue;
                    }

                    if (empty($benefitRating) || !is_numeric($benefitRating) || $benefitRating < 1 || $benefitRating > 10) {
                        $errors[] = "Row {$rowNumber}: Benefit rating must be a number between 1 and 10";
                        continue;
                    }

                    if (empty($speakerRating) || !is_numeric($speakerRating) || $speakerRating < 1 || $speakerRating > 5) {
                        $errors[] = "Row {$rowNumber}: Speaker rating must be a number between 1 and 5";
                        continue;
                    }

                    // Convert would recommend to boolean
                    $wouldRecommendBool = false;
                    if (strtolower($wouldRecommend) === 'yes' || $wouldRecommend === '1' || strtolower($wouldRecommend) === 'true') {
                        $wouldRecommendBool = true;
                    }

                    // Create user feedback record
                    UserFeedback::create([
                        'event_id' => $request->event_id, // This stores the action plan ID
                        'name' => $name, // Required field
                        'phone_number' => $phoneNumber, // Required field
                        'benefit_rating' => (int)$benefitRating,
                        'would_recommend' => $wouldRecommendBool,
                        'most_helpful_topic' => $mostHelpfulTopic ?: null,
                        'speaker_rating' => (int)$speakerRating,
                    ]);

                    // Log suggestions separately if provided (since not stored in DB)
                    if (!empty($suggestions)) {
                        Log::info('Bulk upload suggestions for action plan ' . $request->event_id, [
                            'row' => $rowNumber,
                            'suggestions' => $suggestions,
                            'participant' => $name ?: 'Anonymous'
                        ]);
                    }

                    $importedCount++;

                } catch (\Exception $e) {
                    $errors[] = "Row {$rowNumber}: " . $e->getMessage();
                }
            }

            if (!empty($errors)) {
                Log::warning('Bulk feedback import completed with errors', [
                    'action_plan_id' => $request->event_id,
                    'imported_count' => $importedCount,
                    'errors' => $errors
                ]);

                return response()->json([
                    'success' => true,
                    'imported_count' => $importedCount,
                    'errors' => $errors,
                    'message' => "Imported {$importedCount} feedback entries with " . count($errors) . " errors."
                ]);
            }

            Log::info('Bulk feedback import completed successfully', [
                'action_plan_id' => $request->event_id,
                'imported_count' => $importedCount
            ]);

            return response()->json([
                'success' => true,
                'imported_count' => $importedCount,
                'message' => "Successfully imported {$importedCount} feedback entries."
            ]);

        } catch (\Exception $e) {
            Log::error('Error importing feedback: ' . $e->getMessage(), [
                'scientist_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['error' => 'Failed to import feedback. Please try again.'], 500);
        }
    }

    /**
     * Generate QR code for public feedback.
     */
    public function generateQrCode($eventId)
    {
        try {
            // Verify the action plan belongs to the current scientist
            $actionPlan = ActionPlan::where('id', $eventId)
                ->where('scientist_id', Auth::id())
                ->with(['scientist:id,name,email', 'district:id,state,district'])
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found or access denied'], 404);
            }

            // Check if the action plan is within the 2-day window
            if (!$this->canAddReview($actionPlan)) {
                return response()->json(['error' => 'This action plan is no longer accepting reviews (2 days have passed)'], 403);
            }

            // Generate the feedback URL
            $feedbackUrl = route('public.feedback.actionplan.form', ['actionPlanId' => $eventId]);

            // Generate QR code using online service (fallback for when imagick is not available)
            $qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' . urlencode($feedbackUrl);

            return response()->json([
                'success' => true,
                'action_plan' => $actionPlan,
                'feedback_url' => $feedbackUrl,
                'qr_code_url' => $qrCodeUrl
            ]);
        } catch (\Exception $e) {
            Log::error('Error generating QR code: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to generate QR code'], 500);
        }
    }
}

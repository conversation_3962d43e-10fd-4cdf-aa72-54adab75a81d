<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use App\Http\Controllers\FormBuilderController;
use App\Http\Controllers\InputTypeController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\DistrictUserController;
use App\Http\Controllers\SuperAdmin\DashboardController as SuperAdminDashboardController;
use App\Http\Controllers\Scientist\DashboardController as ScientistDashboardController;
use App\Http\Controllers\Scientist\FormController as ScientistFormController;
use App\Http\Controllers\Scientist\SwotAnalysisController;
use App\Http\Controllers\Scientist\ActionPlanController as ScientistActionPlanController;
use App\Http\Controllers\Scientist\EventFeedbackController;
use App\Http\Controllers\Scientist\CreateTestActionPlanController;
use App\Http\Controllers\Scientist\SyncCompletedActionPlansController;
use App\Http\Controllers\Scientist\ParticipationRequestController;
use App\Http\Controllers\User\DashboardController as UserDashboardController;
use App\Http\Controllers\UserFeedbackController;
use App\Http\Controllers\ActionPlanFeedbackController;
use App\Http\Controllers\ZonalCoordinator\DashboardController as ZonalCoordinatorDashboardController;
use App\Http\Controllers\ZonalCoordinator\ScientistController;
use App\Http\Controllers\ZonalCoordinator\DistrictController;
use App\Http\Controllers\ZonalCoordinator\EventController;
use App\Http\Controllers\ZonalCoordinator\PerformanceController;
use App\Http\Controllers\ZonalCoordinator\VisitController;
use App\Http\Controllers\ZonalCoordinator\ScientistFeedbackController;
use App\Http\Controllers\ZonalCoordinator\FeedbackImportController;
use App\Http\Controllers\ZonalCoordinator\FeedbackAnalyticsController;
use App\Http\Controllers\ZonalCoordinator\NotificationController;

// Home route with redirection based on authentication status and user role
Route::get('/', function () {
    if (Auth::check()) {
        $user = Auth::user();
        $roleRoutes = [
            'super_admin' => 'super-admin.dashboard',
            'admin' => 'admin.dashboard',
            'scientist' => 'scientist.dashboard',
            'zonal_coordinator' => 'zonal-coordinator.dashboard',
            'district_state_coordinator' => 'district-state-coordinator.dashboard',
            'user' => 'user.dashboard',
        ];

        return redirect()->route($roleRoutes[$user->role] ?? 'user.dashboard');
    }

    return redirect()->route('login');
})->name('home');

// Test route (restricted to local environment)
Route::get('/test', function () {
    return 'Test route is working!';
})->middleware('env:local');

// Debug SWOT route (restricted to local environment)
Route::get('/debug-swot', function () {
    try {
        $tableExists = Schema::hasTable('swot');

        if ($tableExists) {
            $swot = new \App\Models\Swot();
            $swot->scientist_id = Auth::id() ?? 1;
            $swot->district_id = 1;
            $swot->strengths = 'Test strengths';
            $swot->weaknesses = 'Test weaknesses';
            $swot->opportunities = 'Test opportunities';
            $swot->threats = 'Test threats';
            $swot->save();

            return response()->json([
                'table_exists' => $tableExists,
                'swot_created' => true,
                'swot_id' => $swot->id,
            ]);
        }

        return response()->json([
            'table_exists' => $tableExists,
            'error' => 'Table does not exist',
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
        ], 500);
    }
})->middleware('env:local');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('login', [LoginController::class, 'login']);
    Route::get('register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('register', [RegisterController::class, 'register']);
});

Route::post('logout', [LoginController::class, 'logout'])->name('logout')->middleware('auth');

// Admin Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('district-users', [DistrictUserController::class, 'index'])->name('district-users');
    Route::get('get-states', [DistrictUserController::class, 'getStates'])->name('get-states');
    Route::get('get-districts/{state}', [DistrictUserController::class, 'getDistrictsByState'])->name('get-districts');
    Route::get('get-district-details/{id}', [DistrictUserController::class, 'getDistrictDetails'])->name('get-district-details');
    Route::post('save-district-user', [DistrictUserController::class, 'saveDistrictUser'])->name('save-district-user');
    Route::get('form-builder', [FormBuilderController::class, 'index'])->name('form-builder');
    Route::get('form-builder/create', [FormBuilderController::class, 'create'])->name('form-builder.create');
    Route::post('form-builder', [FormBuilderController::class, 'store'])->name('form-builder.store');
    Route::delete('form-builder/{id}', [FormBuilderController::class, 'destroy'])->name('form-builder.destroy');
    Route::get('form-builder/{id}/edit', [FormBuilderController::class, 'edit'])->name('form-builder.edit');
    Route::post('form-builder/{id}', [FormBuilderController::class, 'update'])->name('form-builder.update');
});

// Scientist Routes
Route::middleware(['auth', 'role:scientist'])->prefix('scientist')->name('scientist.')->group(function () {
    Route::get('dashboard', [ScientistDashboardController::class, 'index'])->name('dashboard');
    Route::get('get-stats', [ScientistDashboardController::class, 'getStats'])->name('get-stats');
    Route::get('forms', [ScientistFormController::class, 'index'])->name('forms');
    Route::get('forms/data', [ScientistFormController::class, 'getForms'])->name('get-forms');
    Route::get('forms/{id}', [ScientistFormController::class, 'getFormDetails'])->name('get-form');
    Route::get('forms/{id}/read', [ScientistFormController::class, 'read'])->name('read-form');
    Route::post('forms/submit', [InputTypeController::class, 'submitForm'])->name('submit-form');
    Route::get('swot', [SwotAnalysisController::class, 'handleSwotRequest'])->name('swot');
    Route::get('swot/data', [SwotAnalysisController::class, 'getSwotAnalysis'])->name('swot.get');
    Route::post('swot/save', [SwotAnalysisController::class, 'saveSwotAnalysis'])->name('swot.save');
    Route::get('action-plans', [ScientistActionPlanController::class, 'index'])->name('action-plans');
    Route::get('action-plans/data', [ScientistActionPlanController::class, 'getActionPlans'])->name('action-plans.get');
    Route::get('action-plans/{id}', [ScientistActionPlanController::class, 'getActionPlan'])->name('action-plans.get-one');
    Route::get('action-plans/coordinators', [ScientistActionPlanController::class, 'getCoordinators'])->name('action-plans.coordinators');
    Route::post('action-plans', [ScientistActionPlanController::class, 'createActionPlan'])->name('action-plans.create');
    Route::post('action-plans/{id}', [ScientistActionPlanController::class, 'updateActionPlan'])->name('action-plans.update');
    Route::post('action-plans/{id}/cancel', [ScientistActionPlanController::class, 'cancelActionPlan'])->name('action-plans.cancel');
    Route::post('action-plans/{id}/report', [ScientistActionPlanController::class, 'submitReport'])->name('action-plans.report');

    // DSC Invitation Routes
    Route::get('dsc-invitation/{actionPlan}', [\App\Http\Controllers\Scientist\DSCInvitationController::class, 'index'])->name('dsc-invitation');
    Route::get('dsc-invitation/available/dscs', [\App\Http\Controllers\Scientist\DSCInvitationController::class, 'getAvailableDSCs'])->name('dsc-invitation.available');
    Route::post('dsc-invitation/send', [\App\Http\Controllers\Scientist\DSCInvitationController::class, 'sendInvitations'])->name('dsc-invitation.send');
    Route::get('dsc-invitation/{actionPlan}/invited', [\App\Http\Controllers\Scientist\DSCInvitationController::class, 'getInvitedDSCs'])->name('dsc-invitation.invited');

    Route::get('feedback', [EventFeedbackController::class, 'index'])->name('feedback');
    Route::get('feedback/events', [EventFeedbackController::class, 'getCompletedEvents'])->name('feedback.events');
    Route::get('feedback/data', [EventFeedbackController::class, 'getAllFeedback'])->name('feedback.all');
    Route::get('feedback/{id}', [EventFeedbackController::class, 'getFeedback'])->name('feedback.get');
    Route::post('feedback/submit', [EventFeedbackController::class, 'submitFeedback'])->name('feedback.submit');
    Route::get('participation-requests', [ParticipationRequestController::class, 'index'])->name('participation-requests');
    Route::get('participation-requests/data', [ParticipationRequestController::class, 'getRequests'])->name('participation-requests.get');
    Route::get('participation-requests/coordinators', [ParticipationRequestController::class, 'getAvailableCoordinators'])->name('participation-requests.coordinators');
    Route::post('participation-requests', [ParticipationRequestController::class, 'createRequest'])->name('participation-requests.create');
    Route::get('create-test-action-plan', [CreateTestActionPlanController::class, 'createTestActionPlan'])->name('create-test-action-plan');
    Route::get('sync-completed-action-plans', [SyncCompletedActionPlansController::class, 'syncCompletedActionPlans'])->name('sync-completed-action-plans');
    Route::get('debug-action-plans', function () {
        $scientistId = Auth::id();
        $allPlans = \App\Models\ActionPlan::where('scientist_id', $scientistId)->get();
        $completedPlans = \App\Models\ActionPlan::where('scientist_id', $scientistId)->where('status', 'completed')->get();

        return response()->json([
            'scientist_id' => $scientistId,
            'all_plans_count' => $allPlans->count(),
            'all_plans' => $allPlans->map(fn($plan) => [
                'id' => $plan->id,
                'title' => $plan->title,
                'type' => $plan->type,
                'status' => $plan->status,
                'planned_date' => $plan->planned_date,
                'created_at' => $plan->created_at,
            ]),
            'completed_plans_count' => $completedPlans->count(),
            'completed_plans' => $completedPlans->map(fn($plan) => [
                'id' => $plan->id,
                'title' => $plan->title,
                'type' => $plan->type,
                'status' => $plan->status,
                'planned_date' => $plan->planned_date,
                'created_at' => $plan->created_at,
            ]),
        ]);
    })->middleware('env:local');
    Route::get('debug-events', function () {
        $scientistId = Auth::id();
        $allEvents = \App\Models\Event::where('scientist_id', $scientistId)->get();
        $completedEvents = \App\Models\Event::where('scientist_id', $scientistId)->where('status', 'completed')->get();

        return response()->json([
            'scientist_id' => $scientistId,
            'all_events_count' => $allEvents->count(),
            'all_events' => $allEvents->map(fn($event) => [
                'id' => $event->id,
                'title' => $event->title,
                'status' => $event->status,
                'start_date' => $event->start_date,
                'created_at' => $event->created_at,
            ]),
            'completed_events_count' => $completedEvents->count(),
            'completed_events' => $completedEvents->map(fn($event) => [
                'id' => $event->id,
                'title' => $event->title,
                'status' => $event->status,
                'start_date' => $event->start_date,
                'created_at' => $event->created_at,
            ]),
        ]);
    })->middleware('env:local');
});

// User Routes
Route::middleware(['auth', 'role:user'])->prefix('user')->name('user.')->group(function () {
    Route::get('dashboard', [UserDashboardController::class, 'index'])->name('dashboard');
});

// Super Admin Routes
Route::middleware(['auth', 'role:super_admin'])->prefix('super-admin')->name('super-admin.')->group(function () {
    Route::get('dashboard', [SuperAdminDashboardController::class, 'index'])->name('dashboard');
});

// Zonal Coordinator Routes
Route::middleware(['auth', 'role:zonal_coordinator'])->prefix('zonal-coordinator')->name('zonal-coordinator.')->group(function () {
    Route::get('/', [ZonalCoordinatorDashboardController::class, 'index'])->name('dashboard');
    Route::get('dashboard', [ZonalCoordinatorDashboardController::class, 'index'])->name('dashboard');
    Route::prefix('districts')->name('districts.')->group(function () {
        Route::get('/', [DistrictController::class, 'index'])->name('index');
        Route::get('/get-districts', [DistrictController::class, 'getDistricts'])->name('get-districts');
        Route::get('/district/{id}', [DistrictController::class, 'getDistrictDetails'])->name('district');
    });
    Route::prefix('scientists')->name('scientists.')->group(function () {
        Route::get('/', [ScientistController::class, 'index'])->name('index');
        Route::get('/get-scientists', [ScientistController::class, 'getScientists'])->name('get-scientists');
        Route::get('/scientist/{id}', [ScientistController::class, 'getScientistDetails'])->name('scientist');
        Route::get('/get-by-email/{email}', [ScientistController::class, 'getScientistByEmail'])->name('get-by-email');
    });
    Route::prefix('events')->name('events.')->group(function () {
        Route::get('/', [EventController::class, 'index'])->name('index');
        Route::get('/get-events', [EventController::class, 'getEvents'])->name('get-events');
        Route::get('/event/{id}', [EventController::class, 'getEventDetails'])->name('event');
        Route::get('/by-scientist/{id}', [EventController::class, 'getEventsByScientist'])->name('by-scientist');
    });
    Route::prefix('performance')->name('performance.')->group(function () {
        Route::get('/', [PerformanceController::class, 'index'])->name('index');
        Route::get('/data', [PerformanceController::class, 'getPerformanceData'])->name('data');
    });
    Route::prefix('visits')->name('visits.')->group(function () {
        Route::get('/', [VisitController::class, 'index'])->name('index');
        Route::get('/get-visits', [VisitController::class, 'getVisits'])->name('get-visits');
        Route::get('/visit/{id}', [VisitController::class, 'getVisit'])->name('visit');
        Route::post('/create', [VisitController::class, 'createVisit'])->name('create');
        Route::put('/update/{id}', [VisitController::class, 'updateVisit'])->name('update');
        Route::put('/cancel/{id}', [VisitController::class, 'cancelVisit'])->name('cancel');
        Route::post('/submit-report/{id}', [VisitController::class, 'submitReport'])->name('submit-report');
    });
    Route::prefix('scientist-feedback')->name('scientist-feedback.')->group(function () {
        Route::get('/', [ScientistFeedbackController::class, 'index'])->name('index');
        Route::get('/feedback', [ScientistFeedbackController::class, 'getFeedback'])->name('feedback');
        Route::get('/feedback/{id}', [ScientistFeedbackController::class, 'getFeedbackDetails'])->name('feedback.details');
        Route::get('/scientists', [ScientistFeedbackController::class, 'getScientists'])->name('scientists');
        Route::get('/events', [ScientistFeedbackController::class, 'getEvents'])->name('events');
        Route::post('/save', [ScientistFeedbackController::class, 'saveFeedback'])->name('save');
        Route::delete('/delete/{id}', [ScientistFeedbackController::class, 'deleteFeedback'])->name('delete');
    });
    Route::prefix('feedback-import')->name('feedback-import.')->group(function () {
        Route::get('/', [FeedbackImportController::class, 'index'])->name('index');
        Route::get('/get-events', [FeedbackImportController::class, 'getEvents'])->name('get-events');
        Route::get('/event-feedback/{eventId}', [FeedbackImportController::class, 'getEventFeedback'])->name('get-event-feedback');
        Route::get('/download-template', [FeedbackImportController::class, 'downloadTemplate'])->name('download-template');
        Route::post('/import', [FeedbackImportController::class, 'importFeedback'])->name('import');
        Route::get('/generate-qr/{eventId}', [FeedbackImportController::class, 'generateQrCode'])->name('generate-qr');
        Route::get('/test-qr/{eventId}', function($eventId) {
            return response()->json(['test' => 'QR route working', 'eventId' => $eventId]);
        })->name('test-qr');
        Route::get('/export-excel', [FeedbackImportController::class, 'exportExcel'])->name('export-excel');
        Route::get('/export-pdf', [FeedbackImportController::class, 'exportPdf'])->name('export-pdf');
    });
    Route::prefix('feedback-analytics')->name('feedback-analytics.')->group(function () {
        Route::get('/', [FeedbackAnalyticsController::class, 'index'])->name('index');
        Route::get('/data', [FeedbackAnalyticsController::class, 'getAnalyticsData'])->name('data');
        Route::get('/event/{eventId}', [FeedbackAnalyticsController::class, 'getEventAnalytics'])->name('event');
    });
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [NotificationController::class, 'index'])->name('index');
        Route::post('/update-settings', [NotificationController::class, 'updateSettings'])->name('update-settings');
        Route::post('/send-test', [NotificationController::class, 'sendTestNotification'])->name('send-test');
        Route::post('/send-summary', [NotificationController::class, 'sendFeedbackSummary'])->name('send-summary');
    });
    // Action Plans
    Route::prefix('action-plans')->name('action-plans.')->group(function () {
        Route::get('/', [FeedbackImportController::class, 'index'])->name('index');
        Route::get('/data', [FeedbackImportController::class, 'getActionPlans'])->name('data');
    });
});

// Feedback Routes
Route::middleware('auth')->prefix('feedback')->name('feedback.')->group(function () {
    Route::get('{actionPlanId}', [UserFeedbackController::class, 'showFeedbackForm'])->name('form');
    Route::post('submit', [UserFeedbackController::class, 'submitFeedback'])->name('submit');
    Route::get('template/download', [UserFeedbackController::class, 'downloadTemplate'])->name('template.download');
    Route::post('import', [UserFeedbackController::class, 'importFeedback'])->name('import');
    Route::get('list/{actionPlanId}', [UserFeedbackController::class, 'getFeedbackList'])->name('list');
    Route::get('{actionPlanId}/view', [ActionPlanFeedbackController::class, 'viewFeedback'])->name('view');
    Route::get('{actionPlanId}/export', [ActionPlanFeedbackController::class, 'exportFeedback'])->name('export');
    Route::post('{actionPlanId}/import', [ActionPlanFeedbackController::class, 'importFeedback'])->name('import');
});

// Include additional route files
require __DIR__ . '/web_super_admin.php';
require __DIR__ . '/web_district_state_coordinator.php';
require __DIR__ . '/web_public.php';

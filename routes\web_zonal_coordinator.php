<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use App\Http\Controllers\FormBuilderController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\DistrictUserController;
use App\Http\Controllers\ZonalCoordinator\DashboardController as ZonalCoordinatorDashboardController;
use App\Http\Controllers\ZonalCoordinator\ScientistController;
use App\Http\Controllers\ZonalCoordinator\DistrictController;
use App\Http\Controllers\ZonalCoordinator\EventController;
use App\Http\Controllers\ZonalCoordinator\PerformanceController;
use App\Http\Controllers\ZonalCoordinator\VisitController;
use App\Http\Controllers\ZonalCoordinator\ScientistFeedbackController;
use App\Http\Controllers\ZonalCoordinator\FeedbackImportController;
use App\Http\Controllers\ZonalCoordinator\FeedbackAnalyticsController;
use App\Http\Controllers\ZonalCoordinator\NotificationController;

// Home route with redirection based on authentication status and user role
Route::get('/', function () {
    if (Auth::check()) {
        $user = Auth::user();
        $roleRoutes = [
            'super_admin' => 'super-admin.dashboard',
            'admin' => 'admin.dashboard',
            'scientist' => 'scientist.dashboard',
            'zonal_coordinator' => 'zonal-coordinator.dashboard',
            'district_state_coordinator' => 'district-state-coordinator.dashboard',
            'user' => 'user.dashboard',
        ];

        return redirect()->route($roleRoutes[$user->role] ?? 'user.dashboard');
    }

    return redirect()->route('login');
})->name('home');

// Test route (restricted to local environment)
Route::get('/test', function () {
    return 'Test route is working!';
})->middleware('env:local');

// Debug SWOT route (restricted to local environment)
Route::get('/debug-swot', function () {
    try {
        $tableExists = Schema::hasTable('swot');

        if ($tableExists) {
            $swot = new \App\Models\Swot();
            $swot->scientist_id = Auth::id() ?? 1;
            $swot->district_id = 1;
            $swot->strengths = 'Test strengths';
            $swot->weaknesses = 'Test weaknesses';
            $swot->opportunities = 'Test opportunities';
            $swot->threats = 'Test threats';
            $swot->save();

            return response()->json([
                'table_exists' => $tableExists,
                'swot_created' => true,
                'swot_id' => $swot->id,
            ]);
        }

        return response()->json([
            'table_exists' => $tableExists,
            'error' => 'Table does not exist',
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
        ], 500);
    }
})->middleware('env:local');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('login', [LoginController::class, 'login']);
    Route::get('register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('register', [RegisterController::class, 'register']);
});

Route::post('logout', [LoginController::class, 'logout'])->name('logout')->middleware('auth');

// Admin Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('district-users', [DistrictUserController::class, 'index'])->name('district-users');
    Route::get('get-states', [DistrictUserController::class, 'getStates'])->name('get-states');
    Route::get('get-districts/{state}', [DistrictUserController::class, 'getDistrictsByState'])->name('get-districts');
    Route::get('get-district-details/{id}', [DistrictUserController::class, 'getDistrictDetails'])->name('get-district-details');
    Route::post('save-district-user', [DistrictUserController::class, 'saveDistrictUser'])->name('save-district-user');
    Route::get('form-builder', [FormBuilderController::class, 'index'])->name('form-builder');
    Route::get('form-builder/create', [FormBuilderController::class, 'create'])->name('form-builder.create');
    Route::post('form-builder', [FormBuilderController::class, 'store'])->name('form-builder.store');
    Route::delete('form-builder/{id}', [FormBuilderController::class, 'destroy'])->name('form-builder.destroy');
    Route::get('form-builder/{id}/edit', [FormBuilderController::class, 'edit'])->name('form-builder.edit');
    Route::post('form-builder/{id}', [FormBuilderController::class, 'update'])->name('form-builder.update');
});

// Note: Scientist routes are now defined in routes/web.php to avoid duplication

// Note: User and Super Admin routes are defined in routes/web.php to avoid duplication

// Zonal Coordinator Routes
Route::middleware(['auth', 'role:zonal_coordinator'])->prefix('zonal-coordinator')->name('zonal-coordinator.')->group(function () {
    Route::get('/', [ZonalCoordinatorDashboardController::class, 'index'])->name('dashboard');
    Route::get('dashboard', [ZonalCoordinatorDashboardController::class, 'index'])->name('dashboard');
    Route::prefix('districts')->name('districts.')->group(function () {
        Route::get('/', [DistrictController::class, 'index'])->name('index');
        Route::get('/get-districts', [DistrictController::class, 'getDistricts'])->name('get-districts');
        Route::get('/district/{id}', [DistrictController::class, 'getDistrictDetails'])->name('district');
    });
    Route::prefix('scientists')->name('scientists.')->group(function () {
        Route::get('/', [ScientistController::class, 'index'])->name('index');
        Route::get('/get-scientists', [ScientistController::class, 'getScientists'])->name('get-scientists');
        Route::get('/scientist/{id}', [ScientistController::class, 'getScientistDetails'])->name('scientist');
        Route::get('/get-by-email/{email}', [ScientistController::class, 'getScientistByEmail'])->name('get-by-email');
    });
    Route::prefix('events')->name('events.')->group(function () {
        Route::get('/', [EventController::class, 'index'])->name('index');
        Route::get('/get-events', [EventController::class, 'getEvents'])->name('get-events');
        Route::get('/event/{id}', [EventController::class, 'getEventDetails'])->name('event');
        Route::get('/by-scientist/{id}', [EventController::class, 'getEventsByScientist'])->name('by-scientist');
    });
    Route::prefix('performance')->name('performance.')->group(function () {
        Route::get('/', [PerformanceController::class, 'index'])->name('index');
        Route::get('/data', [PerformanceController::class, 'getPerformanceData'])->name('data');
    });
    Route::prefix('visits')->name('visits.')->group(function () {
        Route::get('/', [VisitController::class, 'index'])->name('index');
        Route::get('/get-visits', [VisitController::class, 'getVisits'])->name('get-visits');
        Route::get('/visit/{id}', [VisitController::class, 'getVisit'])->name('visit');
        Route::post('/create', [VisitController::class, 'createVisit'])->name('create');
        Route::put('/update/{id}', [VisitController::class, 'updateVisit'])->name('update');
        Route::put('/cancel/{id}', [VisitController::class, 'cancelVisit'])->name('cancel');
        Route::post('/submit-report/{id}', [VisitController::class, 'submitReport'])->name('submit-report');
    });
    Route::prefix('scientist-feedback')->name('scientist-feedback.')->group(function () {
        Route::get('/', [ScientistFeedbackController::class, 'index'])->name('index');
        Route::get('/feedback', [ScientistFeedbackController::class, 'getFeedback'])->name('feedback');
        Route::get('/feedback/{id}', [ScientistFeedbackController::class, 'getFeedbackDetails'])->name('feedback.details');
        Route::get('/scientists', [ScientistFeedbackController::class, 'getScientists'])->name('scientists');
        Route::get('/events', [ScientistFeedbackController::class, 'getEvents'])->name('events');
        Route::post('/save', [ScientistFeedbackController::class, 'saveFeedback'])->name('save');
        Route::delete('/delete/{id}', [ScientistFeedbackController::class, 'deleteFeedback'])->name('delete');
    });
    Route::prefix('feedback-import')->name('feedback-import.')->group(function () {
        Route::get('/', [FeedbackImportController::class, 'index'])->name('index');
        Route::get('/get-events', [FeedbackImportController::class, 'getEvents'])->name('get-events');
        Route::get('/event-feedback/{eventId}', [FeedbackImportController::class, 'getEventFeedback'])->name('get-event-feedback');
        Route::get('/download-template', [FeedbackImportController::class, 'downloadTemplate'])->name('download-template');
        Route::post('/import', [FeedbackImportController::class, 'importFeedback'])->name('import');
        Route::get('/generate-qr/{eventId}', [FeedbackImportController::class, 'generateQrCode'])->name('generate-qr');
        Route::get('/test-qr/{eventId}', function($eventId) {
            return response()->json(['test' => 'QR route working', 'eventId' => $eventId]);
        })->name('test-qr');
        Route::get('/export-excel', [FeedbackImportController::class, 'exportExcel'])->name('export-excel');
        Route::get('/export-pdf', [FeedbackImportController::class, 'exportPdf'])->name('export-pdf');
    });
    Route::prefix('feedback-analytics')->name('feedback-analytics.')->group(function () {
        Route::get('/', [FeedbackAnalyticsController::class, 'index'])->name('index');
        Route::get('/data', [FeedbackAnalyticsController::class, 'getAnalyticsData'])->name('data');
        Route::get('/event/{eventId}', [FeedbackAnalyticsController::class, 'getEventAnalytics'])->name('event');
    });
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [NotificationController::class, 'index'])->name('index');
        Route::post('/update-settings', [NotificationController::class, 'updateSettings'])->name('update-settings');
        Route::post('/send-test', [NotificationController::class, 'sendTestNotification'])->name('send-test');
        Route::post('/send-summary', [NotificationController::class, 'sendFeedbackSummary'])->name('send-summary');
    });
    // Action Plans
    Route::prefix('action-plans')->name('action-plans.')->group(function () {
        Route::get('/', [FeedbackImportController::class, 'index'])->name('index');
        Route::get('/data', [FeedbackImportController::class, 'getActionPlans'])->name('data');
    });
});

// Note: Feedback routes are defined in routes/web.php to avoid duplication

// Include additional route files
require __DIR__ . '/web_super_admin.php';
require __DIR__ . '/web_district_state_coordinator.php';
require __DIR__ . '/web_public.php';

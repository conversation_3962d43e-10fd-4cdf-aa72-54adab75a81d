@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0" id="formTitle">Form</h5>
                    <a href="{{ route('scientist.forms') }}" class="btn btn-secondary">Back to List</a>
                </div>

                <div class="card-body">
                    <div id="formAlert" class="alert alert-info d-none">
                        <!-- Alert messages will be displayed here -->
                    </div>

                    <form id="formDisplay" enctype="multipart/form-data">
                        <div class="row" id="formFields">
                            <!-- Form fields will be rendered here -->
                        </div>

                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary" id="submitBtn">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Get form ID from URL path
const pathSegments = window.location.pathname.split('/');
const formId = pathSegments[pathSegments.length - 2]; // Get the ID from the URL path

// Load form data when page loads
window.addEventListener('load', function() {
    if (formId) {
        fetch(`/scientist/forms/${formId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('formTitle').textContent = data.form_name;

                // Parse the form structure with robust error handling
                let formStructure;
                try {
                    console.log('Raw form structure data:', data.form_structure);
                    console.log('Type of form structure:', typeof data.form_structure);

                    if (typeof data.form_structure === 'string') {
                        // Try direct JSON parse first
                        try {
                            formStructure = JSON.parse(data.form_structure);
                        } catch (parseError) {
                            console.warn('Direct JSON parse failed, trying to handle double encoding:', parseError);

                            // Try to handle double-encoded JSON
                            try {
                                const firstDecode = JSON.parse(data.form_structure);
                                if (typeof firstDecode === 'string') {
                                    formStructure = JSON.parse(firstDecode);
                                } else {
                                    formStructure = firstDecode;
                                }
                            } catch (doubleParseError) {
                                console.error('Double decode also failed:', doubleParseError);
                                throw new Error('Failed to parse JSON after multiple attempts');
                            }
                        }
                    } else if (Array.isArray(data.form_structure)) {
                        formStructure = data.form_structure;
                    } else if (data.form_structure && typeof data.form_structure === 'object') {
                        // If it's an object but not an array, wrap it in an array
                        formStructure = [data.form_structure];
                    } else {
                        throw new Error('Form structure is not in a recognized format');
                    }

                    // Ensure formStructure is an array
                    if (!Array.isArray(formStructure)) {
                        console.error('Form structure is not an array after parsing:', formStructure);
                        throw new Error('Form structure is not an array');
                    }

                    // Validate that the array contains valid field objects
                    if (formStructure.length === 0) {
                        console.warn('Form structure is empty');
                        alert('This form has no fields defined. Please contact the administrator.');
                        return;
                    }

                    // Validate each field has required properties
                    for (let i = 0; i < formStructure.length; i++) {
                        const field = formStructure[i];
                        if (!field.type || !field.label) {
                            console.error(`Field at index ${i} is missing required properties:`, field);
                            throw new Error(`Field at index ${i} is missing required properties (type or label)`);
                        }
                    }

                    console.log('Successfully parsed and validated form structure:', formStructure);
                } catch (error) {
                    console.error('Error parsing form structure:', error);
                    console.error('Original form structure data:', data.form_structure);
                    alert('Error loading form structure: ' + error.message + '. Please contact support.');
                    return;
                }

                renderForm(formStructure);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while loading the form');
            });
    }
});

function renderForm(fields) {
    const formFields = document.getElementById('formFields');
    formFields.innerHTML = '';

    fields.forEach((field, index) => {
        let fieldHtml = '';

        switch(field.type) {
            case 'text':
                fieldHtml = renderTextField(field, index);
                break;
            case 'number':
                fieldHtml = renderNumberField(field, index);
                break;
            case 'email':
                fieldHtml = renderEmailField(field, index);
                break;
            case 'file':
                fieldHtml = renderFileField(field, index);
                break;
            case 'textarea':
                fieldHtml = renderTextareaField(field, index);
                break;
            case 'select':
                fieldHtml = renderSelectField(field, index);
                break;
            case 'checkbox':
                fieldHtml = renderCheckboxField(field, index);
                break;
            case 'radio':
                fieldHtml = renderRadioField(field, index);
                break;
            default:
                console.warn(`Unsupported field type: ${field.type}`);
                fieldHtml = renderTextField(field, index); // Fallback to text field
                break;
        }

        formFields.insertAdjacentHTML('beforeend', fieldHtml);
    });
}

function renderTextField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <input type="text" class="form-control" id="field_${index}" name="${field.label}"
                placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}>
        </div>
    `;
}

function renderNumberField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <input type="number" class="form-control" id="field_${index}" name="${field.label}"
                placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}>
        </div>
    `;
}

function renderEmailField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <input type="email" class="form-control" id="field_${index}" name="${field.label}"
                placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}>
        </div>
    `;
}

function renderFileField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <input type="file" class="form-control" id="field_${index}" name="${field.label}"
                accept="${field.accept || ''}" ${field.required ? 'required' : ''}>
            <div class="form-text">Accepted file types: ${field.accept || 'All files'}</div>
        </div>
    `;
}

function renderTextareaField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <textarea class="form-control" id="field_${index}" name="${field.label}"
                placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}></textarea>
        </div>
    `;
}

function renderSelectField(field, index) {
    const options = field.options ? field.options.map((option, i) =>
        `<option value="${option}">${option}</option>`
    ).join('') : '';

    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <select class="form-select" id="field_${index}" name="${field.label}" ${field.required ? 'required' : ''}>
                <option value="">Select an option</option>
                ${options}
            </select>
        </div>
    `;
}

function renderCheckboxField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="field_${index}" name="${field.label}" value="1">
                <label class="form-check-label" for="field_${index}">${field.checkboxLabel || field.label}</label>
            </div>
        </div>
    `;
}

function renderRadioField(field, index) {
    const options = field.options ? field.options.map((option, i) => `
        <div class="form-check">
            <input type="radio" class="form-check-input" id="field_${index}_${i}"
                name="${field.label}" value="${option}" ${field.required ? 'required' : ''}>
            <label class="form-check-label" for="field_${index}_${i}">${option}</label>
        </div>
    `).join('') : '';

    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            ${options}
        </div>
    `;
}

document.getElementById('formDisplay').addEventListener('submit', function(e) {
    e.preventDefault();

    const submitBtn = document.getElementById('submitBtn');
    const formAlert = document.getElementById('formAlert');

    // Disable submit button and show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';

    // Show processing message
    formAlert.classList.remove('d-none', 'alert-danger');
    formAlert.classList.add('alert-info');
    formAlert.textContent = 'Processing your submission...';

    // Get the form data
    const formData = new FormData(this);

    // Get the form structure to handle file uploads and other field types
    fetch(`/get-form-builder-edit/${formId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to get form structure');
            }
            return response.json();
        })
        .then(formDetails => {
            // Log the form details for debugging
            console.log('Form details:', formDetails);

            // Parse the form structure with robust error handling
            let formStructure;
            try {
                console.log('Form details for submission:', formDetails);

                if (typeof formDetails.form_structure === 'string') {
                    try {
                        // First attempt: direct JSON parse
                        formStructure = JSON.parse(formDetails.form_structure);
                    } catch (parseError) {
                        console.warn('Initial JSON parse failed, trying double decode', parseError);

                        // Try to handle double-encoded JSON
                        try {
                            const firstDecode = JSON.parse(formDetails.form_structure);
                            if (typeof firstDecode === 'string') {
                                formStructure = JSON.parse(firstDecode);
                            } else {
                                formStructure = firstDecode;
                            }
                        } catch (doubleParseError) {
                            console.error('Double decode also failed', doubleParseError);
                            throw new Error('Failed to parse form structure after multiple attempts');
                        }
                    }
                } else if (Array.isArray(formDetails.form_structure)) {
                    formStructure = formDetails.form_structure;
                } else if (formDetails.form_structure && typeof formDetails.form_structure === 'object') {
                    formStructure = [formDetails.form_structure];
                } else {
                    throw new Error('Form structure is not in a recognized format');
                }

                // Validate the parsed structure
                if (!Array.isArray(formStructure)) {
                    console.error('Form structure is not an array:', formStructure);
                    throw new Error('Form structure is not an array');
                }

                console.log('Successfully parsed form structure for submission:', formStructure);
            } catch (error) {
                console.error('Error parsing form structure for submission:', error, formDetails.form_structure);
                formAlert.classList.remove('alert-info', 'alert-success');
                formAlert.classList.add('alert-danger', 'd-block');
                formAlert.textContent = 'Error parsing form structure: ' + error.message + '. Please contact support.';
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Submit';
                throw error;
            }

            const hasFileFields = formStructure.some(field => field.type === 'file');

            if (hasFileFields) {
                // For forms with file uploads, we need to use FormData
                // Add form ID to the FormData
                formData.append('form_id', formId);

                // Add unchecked checkboxes with value 0
                formStructure.forEach(field => {
                    if (field.type === 'checkbox' && !formData.has(field.label)) {
                        formData.append(field.label, '0');
                    }
                });

                // Submit the form with files
                return fetch('{{ route("scientist.submit-form") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: formData
                });
            } else {
                // For forms without files, we can use JSON
                const data = {};

                // Process form data
                for (let [key, value] of formData.entries()) {
                    // Only include non-empty values
                    if (value !== null && value !== '') {
                        data[key] = value;
                    }
                }

                // Add unchecked checkboxes with value 0
                formStructure.forEach(field => {
                    if (field.type === 'checkbox' && !data.hasOwnProperty(field.label)) {
                        data[field.label] = '0';
                    }
                });

                // Submit the form data as JSON
                return fetch('/scientist/submit-form', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        form_id: formId,
                        form_data: data
                    })
                });
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Server returned an error');
            }
            return response.json();
        })
        .then(responseData => {
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Submit';

            if (responseData.message) {
                // Show success message
                formAlert.classList.remove('alert-info', 'alert-danger');
                formAlert.classList.add('alert-success');
                formAlert.textContent = responseData.message;

                // Redirect after a short delay
                setTimeout(() => {
                    window.location.href = '/scientist/forms';
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Submit';

            // Show error message
            formAlert.classList.remove('alert-info', 'alert-success');
            formAlert.classList.add('alert-danger', 'd-block');
            formAlert.textContent = 'An error occurred while submitting the form. Please try again.';
        });
});
</script>
@endpush
@endsection

<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PublicFeedbackController;

// Public routes that don't require authentication
Route::prefix('public')->name('public.')->group(function () {
    // Feedback routes
    Route::prefix('feedback')->name('feedback.')->group(function () {
        Route::get('/{eventId}', [PublicFeedbackController::class, 'showFeedbackForm'])->name('form');
        Route::post('/{eventId}', [PublicFeedbackController::class, 'submitFeedback'])->name('submit');
        Route::get('/{eventId}/qr', [PublicFeedbackController::class, 'showQrCode'])->name('qr');

        // Action Plan feedback routes
        Route::get('/action-plan/{actionPlanId}', [PublicFeedbackController::class, 'showActionPlanFeedbackForm'])->name('actionplan.form');
        Route::post('/action-plan/{actionPlanId}', [PublicFeedbackController::class, 'submitActionPlanFeedback'])->name('actionplan.submit');
        Route::get('/action-plan/{actionPlanId}/qr', [PublicFeedbackController::class, 'showActionPlanQrCode'])->name('actionplan.qr');
    });
});

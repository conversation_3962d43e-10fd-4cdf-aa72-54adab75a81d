<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScientistFeedback extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'scientist_id',
        'action_plans_id',
        'overall_rating',
        'preparedness_rating',
        'communication_rating',
        'engagement_rating',
        'adherence_rating',
        'remarks',
        'created_by',
        'updated_at',
    ];

    /**
     * Get the scientist associated with this feedback.
     */
    public function scientist()
    {
        return $this->belongsTo(User::class, 'scientist_id');
    }

    /**
     * Get the event associated with this feedback.
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the admin who created this feedback.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the action plan that the feedback is for.
     */
    public function actionPlan()
    {
        return $this->belongsTo(\App\Models\ActionPlan::class, 'action_plan_id');
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Feedback - {{ $actionPlan->title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .feedback-container {
            max-width: 700px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header img {
            max-width: 200px;
            margin-bottom: 20px;
        }
        .event-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 25px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .rating-scale {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
        }
        .rating-option {
            text-align: center;
            flex: 1;
        }
        .rating-option input[type="radio"] {
            margin-bottom: 5px;
        }
        .yes-no-options {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        .yes-no-options label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="feedback-container">
            <div class="header">
                <img src="{{ asset('images/logo.png') }}" alt="Mera Resham Mera Abhiman" onerror="this.src='https://via.placeholder.com/200x80?text=Mera+Resham+Mera+Abhiman'">
                <h2>Event Feedback</h2>
                <p class="text-muted">Your feedback helps us improve our programs</p>
            </div>

            <div class="event-details">
                <h4>{{ $actionPlan->title }}</h4>
                <p><strong>Date:</strong> {{ \Carbon\Carbon::parse($actionPlan->planned_date)->format('d M Y') }}</p>
                <p><strong>Location:</strong> {{ $actionPlan->location }}</p>
                <p><strong>Type:</strong> {{ ucfirst(str_replace('_', ' ', $actionPlan->type)) }}</p>
            </div>

            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form id="feedbackForm" action="{{ route('public.feedback.actionplan.submit', ['actionPlanId' => $actionPlan->id]) }}" method="POST">
                @csrf

                <!-- Required participant details -->
                <div class="form-group">
                    <label for="name" class="form-label">Your Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" value="{{ old('name') }}" required>
                </div>

                <div class="form-group">
                    <label for="phone_number" class="form-label">Phone Number <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="phone_number" name="phone_number" value="{{ old('phone_number') }}" required>
                </div>

                <!-- Question 1: How beneficial was this event for you? (Scale of 1 to 10) -->
                <div class="form-group">
                    <label class="form-label"><strong>1. How beneficial was this event for you?</strong> <span class="text-danger">*</span></label>
                    <p class="text-muted small">Scale of 1 to 10 (1 = Not beneficial at all, 10 = Extremely beneficial)</p>
                    <div class="rating-scale">
                        @for($i = 1; $i <= 10; $i++)
                            <div class="rating-option">
                                <input type="radio" id="benefit_{{ $i }}" name="benefit_rating" value="{{ $i }}" required {{ old('benefit_rating') == $i ? 'checked' : '' }}>
                                <label for="benefit_{{ $i }}">{{ $i }}</label>
                            </div>
                        @endfor
                    </div>
                </div>

                <!-- Question 2: Would you recommend this event to your peers? (Yes/No) -->
                <div class="form-group">
                    <label class="form-label"><strong>2. Would you recommend this event to your peers?</strong> <span class="text-danger">*</span></label>
                    <div class="yes-no-options">
                        <label>
                            <input type="radio" name="would_recommend" value="1" required {{ old('would_recommend') == '1' ? 'checked' : '' }}>
                            <span>Yes</span>
                        </label>
                        <label>
                            <input type="radio" name="would_recommend" value="0" required {{ old('would_recommend') == '0' ? 'checked' : '' }}>
                            <span>No</span>
                        </label>
                    </div>
                </div>

                <!-- Question 3: Which topic did you find most helpful? -->
                <div class="form-group">
                    <label for="most_helpful_topic" class="form-label"><strong>3. Which topic did you find most helpful?</strong></label>
                    <textarea class="form-control" id="most_helpful_topic" name="most_helpful_topic" rows="3" placeholder="Please describe the topic that was most helpful to you...">{{ old('most_helpful_topic') }}</textarea>
                </div>

                <!-- Question 4: Was the speaker/SME helpful and clear in explanation? (Rating scale: 1–5) -->
                <div class="form-group">
                    <label class="form-label"><strong>4. Was the speaker/SME helpful and clear in explanation?</strong> <span class="text-danger">*</span></label>
                    <p class="text-muted small">Rating scale: 1–5 (1 = Poor, 5 = Excellent)</p>
                    <div class="rating-scale">
                        @for($i = 1; $i <= 5; $i++)
                            <div class="rating-option">
                                <input type="radio" id="speaker_{{ $i }}" name="speaker_rating" value="{{ $i }}" required {{ old('speaker_rating') == $i ? 'checked' : '' }}>
                                <label for="speaker_{{ $i }}">{{ $i }}</label>
                            </div>
                        @endfor
                    </div>
                </div>

                <!-- Question 5: Any suggestions for improvement? -->
                <div class="form-group">
                    <label for="suggestions" class="form-label"><strong>5. Any suggestions for improvement?</strong></label>
                    <textarea class="form-control" id="suggestions" name="suggestions" rows="4" placeholder="Please share your suggestions to help us improve future events...">{{ old('suggestions') }}</textarea>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-paper-plane"></i> Submit Feedback
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation and submission
        document.getElementById('feedbackForm').addEventListener('submit', function(e) {
            // Basic validation is handled by HTML5 required attributes
            // You can add custom validation here if needed
        });
    </script>
</body>
</html>

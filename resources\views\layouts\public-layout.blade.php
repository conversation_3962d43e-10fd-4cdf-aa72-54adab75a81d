<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Mera Resham Mera Abhimaan')</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('images/icon.png') }}" type="image/png">
    <link rel="shortcut icon" href="{{ asset('images/icon.png') }}" type="image/png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .nav-link-active {
            background-color: #1e40af;
            color: white;
        }
        .nav-link-inactive {
            color: #1f2937;
        }
        .nav-link-inactive:hover {
            background-color: #e5e7eb;
            color: #1f2937;
        }
        .govt-header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        }
        .banner-slider {
            position: relative;
            overflow: hidden;
        }
        .news-ticker {
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
        }
        .section-title {
            color: #1e40af;
            border-bottom: 2px solid #3b82f6;
            display: inline-block;
            padding-bottom: 4px;
        }
    </style>
</head>
<body class="bg-white">
    <!-- Main Header -->
    <header class="bg-white shadow-sm border-b-2 border-blue-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between py-4">
                <!-- Government Emblem and Logo -->
                <div class="flex items-center space-x-6">
                    <img src="{{ asset('images/Ministry.svg') }}" alt="Government Emblem" class="h-16 ">
                    <img src="{{ asset('images/icon.png') }}" alt="Government Emblem" class="h-16 w-16">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Mera Resham Mera Abhimaan</h1>
                        <p class="text-md font-bold text-gray-700">Central Silk Board</p>
                        <p class="text-sm text-gray-600">Ministry of Textiles, Government of India</p>
                    </div>
                </div>

                <!-- Right side logos/links -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="{{ route('login') }}"
                       class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded text-sm font-medium transition-colors duration-200">
                        Dashboard
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation Menu -->
    <nav class="bg-blue-800 shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-12">
                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-1">
                    <a href="{{ route('public.home') }}"
                       class="px-4 py-3 text-sm font-medium transition-colors duration-200
                              {{ request()->routeIs('public.home') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                        Home
                    </a>
                    <div class="relative group">
                        <a href="{{ route('public.about-us') }}"
                           class="px-4 py-3 text-sm font-medium transition-colors duration-200
                                  {{ request()->routeIs('public.about-us') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                            About Us
                        </a>
                    </div>
                    <a href="{{ route('public.who-is-who') }}"
                       class="px-4 py-3 text-sm font-medium transition-colors duration-200
                              {{ request()->routeIs('public.who-is-who') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                        Who is Who
                    </a>
                    <a href="{{ route('public.contact-us') }}"
                       class="px-4 py-3 text-sm font-medium transition-colors duration-200
                              {{ request()->routeIs('public.contact-us') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                        Contact Us
                    </a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button type="button" class="mobile-menu-button inline-flex items-center justify-center p-2 rounded-md text-blue-100 hover:text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500" aria-controls="mobile-menu" aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="mobile-menu hidden md:hidden bg-blue-900">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="{{ route('public.home') }}"
                   class="block px-3 py-2 text-base font-medium transition-colors duration-200
                          {{ request()->routeIs('public.home') ? 'bg-blue-800 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                    Home
                </a>
                <a href="{{ route('public.about-us') }}"
                   class="block px-3 py-2 text-base font-medium transition-colors duration-200
                          {{ request()->routeIs('public.about-us') ? 'bg-blue-800 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                    About Us
                </a>
                <a href="{{ route('public.who-is-who') }}"
                   class="block px-3 py-2 text-base font-medium transition-colors duration-200
                          {{ request()->routeIs('public.who-is-who') ? 'bg-blue-800 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                    Who is Who
                </a>
                <a href="{{ route('public.contact-us') }}"
                   class="block px-3 py-2 text-base font-medium transition-colors duration-200
                          {{ request()->routeIs('public.contact-us') ? 'bg-blue-800 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                    Contact Us
                </a>
                <a href="{{ route('login') }}"
                   class="block bg-blue-600 hover:bg-blue-500 text-white px-3 py-2 text-base font-medium transition-colors duration-200">
                    Dashboard
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Useful Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-blue-300">Useful Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="{{ route('public.contact-us') }}" class="text-gray-300 hover:text-white transition-colors">Contact Us</a></li>
                        {{-- <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Announcements</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Tenders</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Downloads</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Gallery</a></li> --}}
                    </ul>
                </div>

                <!-- Navigation -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-blue-300">Navigation</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="{{ route('public.home') }}" class="text-gray-300 hover:text-white transition-colors">Home</a></li>
                        <li><a href="{{ route('public.about-us') }}" class="text-gray-300 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="{{ route('public.who-is-who') }}" class="text-gray-300 hover:text-white transition-colors">Who is Who</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Services</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Publications</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-blue-300">Contact Info</h3>
                    <div class="text-gray-300 space-y-2 text-sm">
                        <p><strong>Mera Resham Mera Abhimaan</strong></p>
                        <p>CENTRAL SILK BOARD <br>
                            CSB Complex, B.T.M. Layout, Madivala
                            Bangalore 560 068, Karnataka, INDIA.</p>
                        <p>Government of India</p>
                        <p>Email: <EMAIL></p>
                        <p>Phone: +91-8546-895166</p>
                    </div>
                </div>

                <!-- Social Media -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-blue-300">Social Media</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                    </div>
                    <div class="mt-4 text-sm text-gray-400">
                        <p>Total Visitors: <span class="text-white font-semibold">113,831</span></p>
                        <p>Last Update: {{ date('d-m-Y H:i:s') }}</p>
                    </div>
                </div>
            </div>

            <div class="mt-8 pt-8 border-t border-gray-700">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-300 text-sm">
                        &copy; {{ date('Y') }} Mera Resham Mera Abhimaan. All rights reserved.
                        Designed & Developed by Ministry of Textiles, Government of India.
                    </p>
                    <div class="flex space-x-4 mt-4 md:mt-0">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA4MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjI0IiBmaWxsPSIjMDA3QkZGIi8+Cjx0ZXh0IHg9IjQwIiB5PSIxNSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VmFsaWQgSFRNTDwvdGV4dD4KPC9zdmc+" alt="Valid HTML" class="h-6">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA4MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjI0IiBmaWxsPSIjMDA3QkZGIi8+Cjx0ZXh0IHg9IjQwIiB5PSIxNSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VmFsaWQgQ1NTPC90ZXh0Pgo8L3N2Zz4=" alt="Valid CSS" class="h-6">
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.querySelector('.mobile-menu-button');
            const mobileMenu = document.querySelector('.mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
    </script>

    @stack('scripts')
</body>
</html>

<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\FormBuilder;
use App\Models\FormSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CustomFormBuilderController extends Controller
{
    /**
     * Display the form builder page.
     */
    public function index()
    {
        return view('super-admin.form-builder.index');
    }

    /**
     * Get all forms.
     */
    public function getForms()
    {
        try {
            $forms = FormBuilder::all();
            return response()->json($forms);
        } catch (\Exception $e) {
            Log::error('Error getting forms: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get forms'], 500);
        }
    }

    /**
     * Get form details.
     */
    public function getFormDetails($id)
    {
        try {
            $form = FormBuilder::findOrFail($id);
            return response()->json($form);
        } catch (\Exception $e) {
            Log::error('Error getting form details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get form details'], 500);
        }
    }

    /**
     * Create a new form.
     */
    public function createForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'form_name' => 'required|string|max:255',
            'target_user' => 'required|string|in:all_scientists,pre_cocoon,post_cocoon',
            'form_structure' => 'required|json',
            'last_date' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $form = FormBuilder::create([
                'form_name' => $request->form_name,
                'target_user' => $request->target_user,
                'form_structure' => $request->form_structure,
                'last_date' => $request->last_date,
            ]);

            return response()->json(['message' => 'Form created successfully', 'form' => $form]);
        } catch (\Exception $e) {
            Log::error('Error creating form: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create form'], 500);
        }
    }

    /**
     * Update an existing form.
     */
    public function updateForm(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'form_name' => 'required|string|max:255',
            'target_user' => 'required|string|in:all_scientists,pre_cocoon,post_cocoon',
            'form_structure' => 'required|json',
            'last_date' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $form = FormBuilder::findOrFail($id);
            $form->update([
                'form_name' => $request->form_name,
                'target_user' => $request->target_user,
                'form_structure' => $request->form_structure,
                'last_date' => $request->last_date,
                'last_edited_at' => now(), // Set the last_edited_at timestamp
            ]);

            return response()->json(['message' => 'Form updated successfully', 'form' => $form]);
        } catch (\Exception $e) {
            Log::error('Error updating form: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update form'], 500);
        }
    }

    /**
     * Delete a form.
     */
    public function deleteForm($id)
    {
        try {
            $form = FormBuilder::findOrFail($id);
            $form->delete();

            return response()->json(['message' => 'Form deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Error deleting form: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete form'], 500);
        }
    }

    /**
     * Get submission count for a form.
     */
    public function getSubmissionCount($formId)
    {
        try {
            $form = FormBuilder::findOrFail($formId);
            $formName = $form->form_name;
            $tableName = 'form_' . Str::slug($formName, '_');

            // Check if the form-specific table exists
            if (!Schema::hasTable($tableName)) {
                return response()->json(['count' => 0]);
            }

            // Count submissions from the form-specific table
            $count = DB::table($tableName)->count();
            return response()->json(['count' => $count]);
        } catch (\Exception $e) {
            Log::error('Error getting submission count: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['count' => 0]);
        }
    }

    /**
     * View form submissions.
     */
    public function viewSubmissions($formId)
    {
        try {
            $form = FormBuilder::findOrFail($formId);
            $formName = $form->form_name;

            // Create a table name from the form name (sanitized)
            $tableName = 'form_' . Str::slug($formName, '_');

            // Get submissions directly from the form-specific table
            $submissions = FormSubmission::getFormSubmissions($formId);

            return view('super-admin.form-builder.submissions', [
                'form' => $form,
                'submissions' => $submissions,
                'tableName' => $tableName
            ]);
        } catch (\Exception $e) {
            Log::error('Error viewing submissions: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->route('super-admin.form-builder.index')
                ->with('error', 'Failed to load form submissions: ' . $e->getMessage());
        }
    }

    /**
     * Get submission details.
     */
    public function getSubmissionDetails($submissionId)
    {
        try {
            // Extract form_id from the URL
            $url = request()->headers->get('referer');
            $formId = null;

            if (preg_match('/\/submissions\/(\d+)/', $url, $matches)) {
                $formId = $matches[1];
            }

            if (!$formId) {
                return response()->json(['error' => 'Form ID not found'], 400);
            }

            // Get the submission directly from the form-specific table
            $submission = FormSubmission::getSubmission($submissionId, $formId);

            if (!$submission) {
                return response()->json(['error' => 'Submission not found'], 404);
            }

            return response()->json($submission);
        } catch (\Exception $e) {
            Log::error('Error getting submission details: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to get submission details: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Delete a submission.
     */
    public function toggleSubmissionStatus($submissionId)
    {
        try {
            // Extract form_id from the URL
            $url = request()->headers->get('referer');
            $formId = null;

            if (preg_match('/\/submissions\/(\d+)/', $url, $matches)) {
                $formId = $matches[1];
            }

            if (!$formId) {
                return response()->json(['error' => 'Form ID not found'], 400);
            }

            // Delete the submission
            $deleted = FormSubmission::deleteSubmission($submissionId, $formId);

            if (!$deleted) {
                return response()->json(['error' => 'Failed to delete submission'], 404);
            }

            return response()->json([
                'message' => 'Submission deleted successfully',
                'status' => 'deleted'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting submission: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to delete submission: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Create predefined forms.
     */
    public function createPredefinedForms()
    {
        try {
            // Create Scientist Profile Form
            $this->createPredefinedForm(
                'Scientist Profile Form',
                $this->getScientistProfileFormStructure()
            );

            // Create District Profile Form (Pre-Cocoon)
            $this->createPredefinedForm(
                'District Profile Form (Pre-Cocoon)',
                $this->getDistrictPreCocoonFormStructure()
            );

            // Create District Profile Form (Post-Cocoon)
            $this->createPredefinedForm(
                'District Profile Form (Post-Cocoon)',
                $this->getDistrictPostCocoonFormStructure()
            );

            // Create Sericulture Profile Form (Pre-Cocoon)
            $this->createPredefinedForm(
                'Sericulture Profile Form (Pre-Cocoon)',
                $this->getSericulturePreCocoonFormStructure()
            );

            // Create Sericulture Profile Form (Post-Cocoon)
            $this->createPredefinedForm(
                'Sericulture Profile Form (Post-Cocoon)',
                $this->getSericulturePostCocoonFormStructure()
            );

            return response()->json(['message' => 'Predefined forms created successfully']);
        } catch (\Exception $e) {
            Log::error('Error creating predefined forms: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create predefined forms'], 500);
        }
    }

    /**
     * Create a predefined form.
     */
    private function createPredefinedForm($formName, $formStructure)
    {
        // Determine target user based on form name
        $targetUser = 'all_scientists';
        if (strpos($formName, 'Pre-Cocoon') !== false) {
            $targetUser = 'pre_cocoon';
        } elseif (strpos($formName, 'Post-Cocoon') !== false) {
            $targetUser = 'post_cocoon';
        }

        // Check if the form already exists
        $existingForm = FormBuilder::where('form_name', $formName)->first();

        if ($existingForm) {
            // Update the existing form
            $existingForm->update([
                'target_user' => $targetUser,
                'form_structure' => json_encode($formStructure),
            ]);
        } else {
            // Create a new form
            FormBuilder::create([
                'form_name' => $formName,
                'target_user' => $targetUser,
                'form_structure' => json_encode($formStructure),
            ]);
        }
    }

    /**
     * Get the structure for the Scientist Profile Form.
     */
    private function getScientistProfileFormStructure()
    {
        return [
            [
                'type' => 'text',
                'label' => 'Full Name',
                'required' => true,
                'placeholder' => 'Enter your full name',
            ],
            [
                'type' => 'email',
                'label' => 'Email Address',
                'required' => true,
                'placeholder' => 'Enter your email address',
            ],
            [
                'type' => 'text',
                'label' => 'Phone Number',
                'required' => true,
                'placeholder' => 'Enter your phone number',
            ],
            [
                'type' => 'text',
                'label' => 'Designation',
                'required' => true,
                'placeholder' => 'Enter your designation',
            ],
            [
                'type' => 'textarea',
                'label' => 'Educational Qualifications',
                'required' => true,
                'placeholder' => 'Enter your educational qualifications',
            ],
            [
                'type' => 'textarea',
                'label' => 'Areas of Expertise',
                'required' => true,
                'placeholder' => 'Enter your areas of expertise',
            ],
            [
                'type' => 'textarea',
                'label' => 'Research Experience',
                'required' => false,
                'placeholder' => 'Enter your research experience',
            ],
            [
                'type' => 'textarea',
                'label' => 'Publications',
                'required' => false,
                'placeholder' => 'Enter your publications',
            ],
        ];
    }

    /**
     * Get the structure for the District Profile Form (Pre-Cocoon).
     */
    private function getDistrictPreCocoonFormStructure()
    {
        return [
            [
                'type' => 'text',
                'label' => 'District Name',
                'required' => true,
                'placeholder' => 'Enter district name',
            ],
            [
                'type' => 'text',
                'label' => 'State',
                'required' => true,
                'placeholder' => 'Enter state name',
            ],
            [
                'type' => 'number',
                'label' => 'Total Area (in sq. km)',
                'required' => true,
                'placeholder' => 'Enter total area',
            ],
            [
                'type' => 'number',
                'label' => 'Population',
                'required' => true,
                'placeholder' => 'Enter population',
            ],
            [
                'type' => 'number',
                'label' => 'Number of Mulberry Farmers',
                'required' => true,
                'placeholder' => 'Enter number of mulberry farmers',
            ],
            [
                'type' => 'number',
                'label' => 'Area Under Mulberry Cultivation (in hectares)',
                'required' => true,
                'placeholder' => 'Enter area under mulberry cultivation',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Mulberry Production (in MT)',
                'required' => true,
                'placeholder' => 'Enter annual mulberry production',
            ],
            [
                'type' => 'number',
                'label' => 'Number of Silkworm Rearers',
                'required' => true,
                'placeholder' => 'Enter number of silkworm rearers',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Cocoon Production (in MT)',
                'required' => true,
                'placeholder' => 'Enter annual cocoon production',
            ],
            [
                'type' => 'textarea',
                'label' => 'Major Challenges in Pre-Cocoon Activities',
                'required' => true,
                'placeholder' => 'Enter major challenges',
            ],
            [
                'type' => 'textarea',
                'label' => 'Government Schemes Available',
                'required' => true,
                'placeholder' => 'Enter government schemes available',
            ],
        ];
    }

    /**
     * Get the structure for the District Profile Form (Post-Cocoon).
     */
    private function getDistrictPostCocoonFormStructure()
    {
        return [
            [
                'type' => 'text',
                'label' => 'District Name',
                'required' => true,
                'placeholder' => 'Enter district name',
            ],
            [
                'type' => 'text',
                'label' => 'State',
                'required' => true,
                'placeholder' => 'Enter state name',
            ],
            [
                'type' => 'number',
                'label' => 'Number of Reeling Units',
                'required' => true,
                'placeholder' => 'Enter number of reeling units',
            ],
            [
                'type' => 'number',
                'label' => 'Number of Weaving Units',
                'required' => true,
                'placeholder' => 'Enter number of weaving units',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Raw Silk Production (in MT)',
                'required' => true,
                'placeholder' => 'Enter annual raw silk production',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Silk Fabric Production (in sq. meters)',
                'required' => true,
                'placeholder' => 'Enter annual silk fabric production',
            ],
            [
                'type' => 'number',
                'label' => 'Number of Artisans Involved in Post-Cocoon Activities',
                'required' => true,
                'placeholder' => 'Enter number of artisans',
            ],
            [
                'type' => 'textarea',
                'label' => 'Major Challenges in Post-Cocoon Activities',
                'required' => true,
                'placeholder' => 'Enter major challenges',
            ],
            [
                'type' => 'textarea',
                'label' => 'Government Schemes Available',
                'required' => true,
                'placeholder' => 'Enter government schemes available',
            ],
            [
                'type' => 'textarea',
                'label' => 'Market Linkages',
                'required' => true,
                'placeholder' => 'Enter market linkages',
            ],
        ];
    }

    /**
     * Get the structure for the Sericulture Profile Form (Pre-Cocoon).
     */
    private function getSericulturePreCocoonFormStructure()
    {
        return [
            [
                'type' => 'text',
                'label' => 'Farmer Name',
                'required' => true,
                'placeholder' => 'Enter farmer name',
            ],
            [
                'type' => 'text',
                'label' => 'Village',
                'required' => true,
                'placeholder' => 'Enter village name',
            ],
            [
                'type' => 'text',
                'label' => 'District',
                'required' => true,
                'placeholder' => 'Enter district name',
            ],
            [
                'type' => 'text',
                'label' => 'State',
                'required' => true,
                'placeholder' => 'Enter state name',
            ],
            [
                'type' => 'number',
                'label' => 'Land Holding (in acres)',
                'required' => true,
                'placeholder' => 'Enter land holding',
            ],
            [
                'type' => 'number',
                'label' => 'Area Under Mulberry Cultivation (in acres)',
                'required' => true,
                'placeholder' => 'Enter area under mulberry cultivation',
            ],
            [
                'type' => 'select',
                'label' => 'Mulberry Variety',
                'required' => true,
                'options' => ['V1', 'S36', 'S54', 'G4', 'Other'],
            ],
            [
                'type' => 'select',
                'label' => 'Irrigation Method',
                'required' => true,
                'options' => ['Drip', 'Sprinkler', 'Flood', 'Rainfed', 'Other'],
            ],
            [
                'type' => 'number',
                'label' => 'Number of Crops per Year',
                'required' => true,
                'placeholder' => 'Enter number of crops per year',
            ],
            [
                'type' => 'select',
                'label' => 'Silkworm Race',
                'required' => true,
                'options' => ['Bivoltine', 'Multivoltine', 'Cross Breed', 'Other'],
            ],
            [
                'type' => 'number',
                'label' => 'Average Cocoon Yield (in kg per 100 DFLs)',
                'required' => true,
                'placeholder' => 'Enter average cocoon yield',
            ],
            [
                'type' => 'textarea',
                'label' => 'Major Diseases Encountered',
                'required' => false,
                'placeholder' => 'Enter major diseases encountered',
            ],
            [
                'type' => 'textarea',
                'label' => 'Challenges Faced',
                'required' => true,
                'placeholder' => 'Enter challenges faced',
            ],
        ];
    }

    /**
     * Get the structure for the Sericulture Profile Form (Post-Cocoon).
     */
    private function getSericulturePostCocoonFormStructure()
    {
        return [
            [
                'type' => 'text',
                'label' => 'Reeler/Weaver Name',
                'required' => true,
                'placeholder' => 'Enter reeler/weaver name',
            ],
            [
                'type' => 'text',
                'label' => 'Village',
                'required' => true,
                'placeholder' => 'Enter village name',
            ],
            [
                'type' => 'text',
                'label' => 'District',
                'required' => true,
                'placeholder' => 'Enter district name',
            ],
            [
                'type' => 'text',
                'label' => 'State',
                'required' => true,
                'placeholder' => 'Enter state name',
            ],
            [
                'type' => 'select',
                'label' => 'Type of Activity',
                'required' => true,
                'options' => ['Reeling', 'Weaving', 'Both', 'Other'],
            ],
            [
                'type' => 'select',
                'label' => 'Type of Reeling Unit',
                'required' => false,
                'options' => ['Charka', 'Cottage Basin', 'Multi-end', 'Automatic', 'Not Applicable'],
            ],
            [
                'type' => 'number',
                'label' => 'Number of Basins/Looms',
                'required' => true,
                'placeholder' => 'Enter number of basins/looms',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Raw Silk Production (in kg)',
                'required' => false,
                'placeholder' => 'Enter annual raw silk production',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Fabric Production (in meters)',
                'required' => false,
                'placeholder' => 'Enter annual fabric production',
            ],
            [
                'type' => 'select',
                'label' => 'Source of Cocoons',
                'required' => false,
                'options' => ['Local Market', 'Government Cocoon Market', 'Direct from Farmers', 'Other'],
            ],
            [
                'type' => 'select',
                'label' => 'Market for Products',
                'required' => true,
                'options' => ['Local Market', 'Traders', 'Export', 'Government Agencies', 'Other'],
            ],
            [
                'type' => 'textarea',
                'label' => 'Challenges Faced',
                'required' => true,
                'placeholder' => 'Enter challenges faced',
            ],
            [
                'type' => 'textarea',
                'label' => 'Support Required',
                'required' => true,
                'placeholder' => 'Enter support required',
            ],
        ];
    }
}

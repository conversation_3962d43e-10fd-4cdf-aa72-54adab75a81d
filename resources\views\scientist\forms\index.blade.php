@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Available Forms') }}</h5>
                    <a href="{{ route('scientist.dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Below are the forms available for you to fill out based on your district assignment.</li>
                            <li>Click on a form to view and fill it out.</li>
                            <li>Your submissions will be saved and can be reviewed by administrators.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Form Name</th>
                                    <th>Fields</th>
                                    <th>Last Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="formsTableBody">
                                <!-- Forms will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const formsTableBody = document.getElementById('formsTableBody');

        // Load forms
        loadForms();

        // Load forms function
        function loadForms() {
            fetch('{{ route("scientist.get-forms") }}')
                .then(response => response.json())
                .then(data => {
                    formsTableBody.innerHTML = '';

                    if (data.length === 0) {
                        formsTableBody.innerHTML = '<tr><td colspan="4" class="text-center">No forms available for you at this time.</td></tr>';
                        return;
                    }

                    data.forEach(form => {
                        const row = document.createElement('tr');

                        // Parse form structure
                        let formStructure = [];
                        try {
                            formStructure = JSON.parse(form.form_structure);
                        } catch (e) {
                            console.error('Error parsing form structure:', e);
                        }

                        // Format date
                        const updatedDate = new Date(form.updated_at);
                        const formattedDate = updatedDate.toLocaleDateString() + ' ' + updatedDate.toLocaleTimeString();

                        // Determine button state based on submission status
                        let buttonHtml = '';

                        if (form.already_submitted && !form.can_resubmit) {
                            // Form already submitted and cannot be resubmitted
                            buttonHtml = `
                                <button class="btn btn-sm btn-secondary" disabled>
                                    <i class="bi bi-check-circle"></i> Already Submitted
                                </button>
                            `;
                        } else if (form.already_submitted && form.can_resubmit) {
                            // Form already submitted but can be resubmitted (form was edited)
                            buttonHtml = `
                                <a href="{{ route('scientist.read-form', ['id' => ':id']) }}".replace(':id', form.id) class="btn btn-sm btn-warning">
                                    <i class="bi bi-pencil"></i> Update Submission
                                </a>
                                <div class="small text-muted mt-1">Form has been updated by admin</div>
                            `;
                        } else {
                            // Form not submitted yet
                            buttonHtml = `
                                <a href="{{ route('scientist.read-form', ['id' => ':id']) }}".replace(':id', form.id) class="btn btn-sm btn-primary">
                                    <i class="bi bi-pencil"></i> Fill Form
                                </a>
                            `;
                        }

                        row.innerHTML = `
                            <td>${form.form_name}</td>
                            <td>${formStructure.length} fields</td>
                            <td>${formattedDate}</td>
                            <td>
                                ${buttonHtml}
                            </td>
                        `;

                        formsTableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error loading forms:', error);
                    formsTableBody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">Error loading forms. Please try again later.</td></tr>';
                });
        }
    });
</script>
@endpush

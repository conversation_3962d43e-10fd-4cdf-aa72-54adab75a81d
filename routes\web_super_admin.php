<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SuperAdmin\DashboardController;
use App\Http\Controllers\SuperAdmin\DistrictManagementController;
use App\Http\Controllers\SuperAdmin\ScientistManagementController;
use App\Http\Controllers\SuperAdmin\DistrictStateCoordinatorController;
use App\Http\Controllers\SuperAdmin\ZonalCoordinatorController;
use App\Http\Controllers\SuperAdmin\EventManagementController;
use App\Http\Controllers\SuperAdmin\ScientistFeedbackController;
use App\Http\Controllers\SuperAdmin\PerformanceDashboardController;
use App\Http\Controllers\SuperAdmin\CustomFormBuilderController;

// Super Admin Routes
Route::middleware(['auth', 'role:super_admin'])->prefix('super-admin')->name('super-admin.')->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // District Management
    Route::prefix('district-management')->name('district-management.')->group(function () {
        Route::get('/', [DistrictManagementController::class, 'index'])->name('index');
        Route::get('/states', [DistrictManagementController::class, 'getStates'])->name('states');
        Route::get('/districts/{state}', [DistrictManagementController::class, 'getDistricts'])->name('districts');
        Route::get('/district/{id}', [DistrictManagementController::class, 'getDistrictDetails'])->name('district');
        Route::post('/save', [DistrictManagementController::class, 'saveDistrictScientist'])->name('save');
    });

    // Scientist Management
    Route::prefix('scientist-management')->name('scientist-management.')->group(function () {
        Route::get('/', [ScientistManagementController::class, 'index'])->name('index');
        Route::get('/scientists', [ScientistManagementController::class, 'getScientists'])->name('scientists');
        Route::get('/scientist/{id}', [ScientistManagementController::class, 'getScientistDetails'])->name('scientist');
        Route::post('/save', [ScientistManagementController::class, 'saveScientist'])->name('save');
        Route::delete('/delete/{id}', [ScientistManagementController::class, 'deleteScientist'])->name('delete');
    });

    // District State Coordinator Management
    Route::prefix('district-state-coordinator')->name('district-state-coordinator.')->group(function () {
        Route::get('/', [DistrictStateCoordinatorController::class, 'index'])->name('index');
        Route::get('/all', [DistrictStateCoordinatorController::class, 'getAll'])->name('all');
        Route::get('/details/{id}', [DistrictStateCoordinatorController::class, 'getDetails'])->name('details');
        Route::post('/save', [DistrictStateCoordinatorController::class, 'save'])->name('save');
        Route::delete('/delete/{id}', [DistrictStateCoordinatorController::class, 'delete'])->name('delete');
        Route::get('/available-districts', [DistrictStateCoordinatorController::class, 'getAvailableDistricts'])->name('available-districts');
    });

    // Zonal Coordinator Management
    Route::prefix('zonal-coordinator')->name('zonal-coordinator.')->group(function () {
        Route::get('/', [ZonalCoordinatorController::class, 'index'])->name('index');
        Route::get('/coordinators', [ZonalCoordinatorController::class, 'getCoordinators'])->name('coordinators');
        Route::get('/coordinator/{id}', [ZonalCoordinatorController::class, 'getCoordinatorDetails'])->name('coordinator');
        Route::post('/save', [ZonalCoordinatorController::class, 'saveCoordinator'])->name('save');
        Route::delete('/delete/{id}', [ZonalCoordinatorController::class, 'deleteCoordinator'])->name('delete');
        Route::get('/available-districts', [ZonalCoordinatorController::class, 'getAvailableDistricts'])->name('available-districts');
    });

    // Event Management
    Route::prefix('event-management')->name('event-management.')->group(function () {
        Route::get('/', [EventManagementController::class, 'index'])->name('index');
        Route::get('/events', [EventManagementController::class, 'getEvents'])->name('events');
        Route::get('/event/{id}', [EventManagementController::class, 'getEventDetails'])->name('event');
        Route::post('/save', [EventManagementController::class, 'saveEvent'])->name('save');
        Route::delete('/delete/{id}', [EventManagementController::class, 'deleteEvent'])->name('delete');
        Route::get('/scientists', [EventManagementController::class, 'getScientists'])->name('scientists');
        Route::get('/districts', [EventManagementController::class, 'getDistricts'])->name('districts');
    });

    // Scientist Feedback
    Route::prefix('scientist-feedback')->name('scientist-feedback.')->group(function () {
        Route::get('/', [ScientistFeedbackController::class, 'index'])->name('index');
        Route::get('/all', [ScientistFeedbackController::class, 'getAllFeedback'])->name('all');
        Route::get('/details/{id}', [ScientistFeedbackController::class, 'getFeedbackDetails'])->name('details');
        Route::post('/save', [ScientistFeedbackController::class, 'saveFeedback'])->name('save');
        Route::delete('/delete/{id}', [ScientistFeedbackController::class, 'deleteFeedback'])->name('delete');
        Route::get('/scientists', [ScientistFeedbackController::class, 'getScientists'])->name('scientists');
        Route::get('/events', [ScientistFeedbackController::class, 'getEvents'])->name('events');
    });

    // Performance Dashboard
    Route::prefix('performance-dashboard')->name('performance-dashboard.')->group(function () {
        Route::get('/', [PerformanceDashboardController::class, 'index'])->name('index');
        Route::get('/data', [PerformanceDashboardController::class, 'getPerformanceData'])->name('data');
    });

    // Custom Form Builder
    Route::prefix('form-builder')->name('form-builder.')->group(function () {
        Route::get('/', [CustomFormBuilderController::class, 'index'])->name('index');
        Route::get('/forms', [CustomFormBuilderController::class, 'getForms'])->name('forms');
        Route::get('/form/{id}', [CustomFormBuilderController::class, 'getFormDetails'])->name('form');
        Route::post('/create', [CustomFormBuilderController::class, 'createForm'])->name('create');
        Route::put('/update/{id}', [CustomFormBuilderController::class, 'updateForm'])->name('update');
        Route::delete('/delete/{id}', [CustomFormBuilderController::class, 'deleteForm'])->name('delete');
        Route::post('/create-predefined', [CustomFormBuilderController::class, 'createPredefinedForms'])->name('create-predefined');
        Route::get('/submission-count/{formId}', [CustomFormBuilderController::class, 'getSubmissionCount'])->name('submission-count');
        Route::get('/submissions/{formId}', [CustomFormBuilderController::class, 'viewSubmissions'])->name('submissions');
        Route::get('/submission/{submissionId}', [CustomFormBuilderController::class, 'getSubmissionDetails'])->name('submission');
        Route::put('/submission/{submissionId}/toggle-status', [CustomFormBuilderController::class, 'toggleSubmissionStatus'])->name('toggle-submission-status');
    });
});
